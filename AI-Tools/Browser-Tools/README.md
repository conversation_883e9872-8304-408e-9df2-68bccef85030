# 🌐 Browser Tools Configuration

## 📦 Installed Tools

### **Browser-sync**
- **Purpose:** Live reload development server
- **Port:** 3003 (main), 3004 (UI)
- **Features:** Real-time browser refresh, multi-device sync
- **Config:** `bs-config.js` in Live-Viewer folder

### **Live-server**
- **Purpose:** Alternative development server
- **Port:** 3002
- **Features:** Simple HTTP server with live reload
- **Usage:** Backup option for development

### **Vite**
- **Purpose:** Ultra-fast build tool
- **Features:** Hot module replacement, optimized builds
- **Usage:** Modern web development builds

---

## 🚀 Quick Commands

### **Start Live Viewer:**
```bash
cd ~/Desktop/AI-Projects/Live-Viewer
npm run start
```

### **Alternative Servers:**
```bash
# Live-server (port 3002)
npm run live

# Vite development
npm run dev

# Vite build
npm run build
```

---

## ⚙️ Configuration

### **Browser-sync Config (bs-config.js):**
- CORS enabled for AI integration
- Auto-reload on file changes
- Multi-device synchronization
- Custom middleware for logging

### **Package.json Scripts:**
```json
{
  "start": "browser-sync start --config bs-config.js",
  "dev": "concurrently \"npm run start\" \"npm run watch\"",
  "live": "live-server --port=3002 --open=live-viewer.html",
  "build": "vite build",
  "watch": "nodemon --watch . --ext html,css,js,json"
}
```

---

## 🎯 Features

### **Real-time Development:**
- ✅ Instant browser refresh on code changes
- ✅ Live CSS injection
- ✅ Multi-device testing
- ✅ Network access for mobile testing

### **AI Integration:**
- ✅ Optimized for Augment AI workflow
- ✅ CORS configured for API calls
- ✅ Real-time feedback system
- ✅ Visual development environment

### **Performance:**
- ✅ Fast startup times
- ✅ Minimal resource usage
- ✅ Efficient file watching
- ✅ Optimized for development

---

## 🔧 Troubleshooting

### **Port conflicts:**
```bash
# Kill processes on ports
lsof -ti:3003 | xargs kill -9
lsof -ti:3004 | xargs kill -9
```

### **File watching issues:**
```bash
# Increase file watch limit
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
```

### **Browser not opening:**
- Check firewall settings
- Verify localhost access
- Try different browser

---

**Browser tools are configured for optimal AI development! 🚀**
