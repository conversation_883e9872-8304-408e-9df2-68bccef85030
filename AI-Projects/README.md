# 🤖 AI Projects - Desktop Organization

## 📁 Folder Structure

### **AI-Projects/**
- **Live-Viewer/** - Visual live development environment
- **Games/** - AI-generated games and interactive projects  
- **Demos/** - Demo applications and examples

### **AI-Tools/**
- **Browser-Tools/** - Browser-sync, live-server, development tools
- **Development-Tools/** - Vite, nodemon, concurrently
- **Performance-Tools/** - Stats app and monitoring tools

### **MCP-Servers/**
- **Ollama/** - AI model server and configurations
- **Docker/** - Container configurations and setups
- **AnythingLLM/** - LLM interface and tools

### **Software-Downloads/**
- **System-Tools/** - Downloaded applications and utilities
- **AI-Models/** - AI model files and configurations

### **Game-Projects/**
- **3D-Games/** - 3D games and physics simulators
- **Web-Games/** - Browser-based games and interactive apps

---

## 🚀 Quick Access

### **Start Live Viewer:**
```bash
cd ~/Desktop/AI-Projects/Live-Viewer
npm run start
```

### **Play 3D Car Game:**
```bash
open ~/Desktop/Game-Projects/3D-Games/3d-car-game.html
```

### **Access AI Model:**
```bash
ollama run gemma2:2b
```

---

## 📊 Current Setup

- **AI Model:** Gemma2-2B (1.6GB, 4x faster)
- **Live Viewer:** http://localhost:3003
- **System Monitor:** Stats app (toolbar)
- **Development Tools:** All installed and configured

---

## 🎯 Features

- ✅ Real-time visual development
- ✅ 3D game development
- ✅ AI model optimization
- ✅ Background system monitoring
- ✅ Organized file structure
- ✅ Easy desktop access

---

**Everything is now organized on your Desktop for easy access! 🎉**
