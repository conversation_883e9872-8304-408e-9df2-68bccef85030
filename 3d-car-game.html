<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Car Driving Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #228B22 100%);
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
        }
        
        #speedometer {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 120px;
            height: 120px;
            border: 3px solid white;
            border-radius: 50%;
            background: rgba(0,0,0,0.7);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div>🏁 3D Car Racing Game</div>
            <div>Score: <span id="score">0</span></div>
            <div>Distance: <span id="distance">0</span>m</div>
        </div>
        
        <div id="controls">
            <div>🎮 Controls:</div>
            <div>↑↓ - Accelerate/Brake</div>
            <div>←→ - Steer</div>
            <div>SPACE - Handbrake</div>
        </div>
        
        <div id="speedometer">
            <div>
                <div style="font-size: 12px;">SPEED</div>
                <div id="speed">0</div>
                <div style="font-size: 12px;">KM/H</div>
            </div>
        </div>
    </div>

    <script>
        // Game setup
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // Game state
        const game = {
            score: 0,
            distance: 0,
            speed: 0,
            maxSpeed: 200,
            acceleration: 0.5,
            deceleration: 0.3,
            turnSpeed: 0.05
        };
        
        // Car object
        const car = {
            x: canvas.width / 2,
            y: canvas.height * 0.8,
            width: 40,
            height: 80,
            angle: 0,
            velocity: { x: 0, y: 0 },
            speed: 0,
            maxSpeed: 8,
            acceleration: 0.2,
            friction: 0.95,
            turnSpeed: 0.03
        };
        
        // Road and environment
        const road = {
            width: 400,
            lanes: 3,
            centerX: canvas.width / 2,
            offset: 0,
            curve: 0,
            curviness: 0.02
        };
        
        // Traffic cars
        const traffic = [];
        const maxTraffic = 5;

        // Power-ups
        const powerUps = [];
        const powerUpTypes = [
            { type: 'speed', color: '#FFD700', effect: 'Speed Boost!' },
            { type: 'score', color: '#00FF00', effect: 'Score Multiplier!' },
            { type: 'shield', color: '#00BFFF', effect: 'Shield Active!' }
        ];

        // Sound effects (Web Audio API)
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function playSound(frequency, duration, type = 'sine') {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // Game effects
        let shieldActive = false;
        let speedBoost = false;
        let scoreMultiplier = 1;
        
        // Input handling
        const keys = {};
        
        window.addEventListener('keydown', (e) => {
            keys[e.code] = true;
        });
        
        window.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });
        
        // Create traffic car
        function createTrafficCar() {
            const lanes = [-100, 0, 100]; // Lane positions relative to road center
            const lane = lanes[Math.floor(Math.random() * lanes.length)];

            return {
                x: road.centerX + lane,
                y: -100,
                width: 35,
                height: 70,
                speed: Math.random() * 2 + 1,
                color: `hsl(${Math.random() * 360}, 70%, 50%)`
            };
        }

        // Create power-up
        function createPowerUp() {
            const lanes = [-100, 0, 100];
            const lane = lanes[Math.floor(Math.random() * lanes.length)];
            const powerUpType = powerUpTypes[Math.floor(Math.random() * powerUpTypes.length)];

            return {
                x: road.centerX + lane,
                y: -50,
                width: 30,
                height: 30,
                speed: 2,
                type: powerUpType.type,
                color: powerUpType.color,
                effect: powerUpType.effect,
                rotation: 0
            };
        }
        
        // Update traffic and power-ups
        function updateTraffic() {
            // Add new traffic cars
            if (traffic.length < maxTraffic && Math.random() < 0.02) {
                traffic.push(createTrafficCar());
            }

            // Add power-ups occasionally
            if (powerUps.length < 2 && Math.random() < 0.005) {
                powerUps.push(createPowerUp());
            }

            // Update existing traffic
            for (let i = traffic.length - 1; i >= 0; i--) {
                const trafficCar = traffic[i];
                trafficCar.y += trafficCar.speed + car.speed * 0.5;

                // Remove cars that are off screen
                if (trafficCar.y > canvas.height + 100) {
                    traffic.splice(i, 1);
                    game.score += 10 * scoreMultiplier;
                }
            }

            // Update power-ups
            for (let i = powerUps.length - 1; i >= 0; i--) {
                const powerUp = powerUps[i];
                powerUp.y += powerUp.speed + car.speed * 0.5;
                powerUp.rotation += 0.1;

                // Remove power-ups that are off screen
                if (powerUp.y > canvas.height + 100) {
                    powerUps.splice(i, 1);
                }

                // Check power-up collision
                if (checkCollision(car, powerUp)) {
                    activatePowerUp(powerUp);
                    powerUps.splice(i, 1);
                    playSound(800, 0.2, 'square'); // Power-up sound
                }
            }
        }

        // Activate power-up effects
        function activatePowerUp(powerUp) {
            switch (powerUp.type) {
                case 'speed':
                    speedBoost = true;
                    car.maxSpeed = 12;
                    setTimeout(() => {
                        speedBoost = false;
                        car.maxSpeed = 8;
                    }, 3000);
                    break;
                case 'score':
                    scoreMultiplier = 2;
                    setTimeout(() => {
                        scoreMultiplier = 1;
                    }, 5000);
                    break;
                case 'shield':
                    shieldActive = true;
                    setTimeout(() => {
                        shieldActive = false;
                    }, 4000);
                    break;
            }

            // Show effect message
            showMessage(powerUp.effect);
        }

        // Show temporary message
        let currentMessage = '';
        let messageTimer = 0;

        function showMessage(text) {
            currentMessage = text;
            messageTimer = 120; // 2 seconds at 60fps
        }
        
        // Check collision
        function checkCollision(rect1, rect2) {
            return rect1.x < rect2.x + rect2.width &&
                   rect1.x + rect1.width > rect2.x &&
                   rect1.y < rect2.y + rect2.height &&
                   rect1.y + rect1.height > rect2.y;
        }
        
        // Update car physics
        function updateCar() {
            // Handle input
            if (keys['ArrowUp'] || keys['KeyW']) {
                car.speed = Math.min(car.speed + car.acceleration, car.maxSpeed);
            }
            if (keys['ArrowDown'] || keys['KeyS']) {
                car.speed = Math.max(car.speed - car.acceleration * 1.5, -car.maxSpeed * 0.5);
            }
            if (keys['ArrowLeft'] || keys['KeyA']) {
                if (car.speed !== 0) {
                    car.angle -= car.turnSpeed * (car.speed / car.maxSpeed);
                    car.x -= 2;
                }
            }
            if (keys['ArrowRight'] || keys['KeyD']) {
                if (car.speed !== 0) {
                    car.angle += car.turnSpeed * (car.speed / car.maxSpeed);
                    car.x += 2;
                }
            }
            if (keys['Space']) {
                car.speed *= 0.9; // Handbrake
            }
            
            // Apply friction
            car.speed *= car.friction;
            
            // Keep car on screen
            car.x = Math.max(road.centerX - road.width/2 + car.width/2, 
                           Math.min(car.x, road.centerX + road.width/2 - car.width/2));
            
            // Update game stats
            game.speed = Math.abs(car.speed * 25); // Convert to km/h for display
            game.distance += Math.abs(car.speed * 0.1);
            
            // Check traffic collisions
            for (const trafficCar of traffic) {
                if (checkCollision(car, trafficCar)) {
                    if (!shieldActive) {
                        // Collision effects
                        car.speed *= 0.3;
                        game.score = Math.max(0, game.score - 50);

                        // Create crash particles
                        for (let i = 0; i < 10; i++) {
                            createParticle(car.x + car.width/2, car.y + car.height/2, 'crash');
                        }

                        // Crash sound
                        playSound(150, 0.5, 'sawtooth');

                        showMessage('CRASH! -50 points');
                    } else {
                        // Shield deflection
                        showMessage('Shield Protected!');
                        playSound(600, 0.3, 'triangle');
                    }
                }
            }
        }
        
        // Draw car with effects
        function drawCar(x, y, width, height, angle, color = '#ff4444', isPlayer = false) {
            ctx.save();
            ctx.translate(x + width/2, y + height/2);
            ctx.rotate(angle);

            // Shield effect for player
            if (isPlayer && shieldActive) {
                ctx.strokeStyle = '#00BFFF';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(0, 0, width * 0.8, 0, Math.PI * 2);
                ctx.stroke();

                ctx.strokeStyle = '#87CEEB';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(0, 0, width * 0.9, 0, Math.PI * 2);
                ctx.stroke();
            }

            // Speed boost effect
            if (isPlayer && speedBoost) {
                ctx.shadowColor = '#FFD700';
                ctx.shadowBlur = 10;
            }

            // Car body
            ctx.fillStyle = color;
            ctx.fillRect(-width/2, -height/2, width, height);

            // Car details
            ctx.fillStyle = '#333';
            ctx.fillRect(-width/2 + 5, -height/2 + 10, width - 10, height - 20);

            // Wheels
            ctx.fillStyle = '#000';
            ctx.fillRect(-width/2 - 3, -height/2 + 5, 6, 15);
            ctx.fillRect(width/2 - 3, -height/2 + 5, 6, 15);
            ctx.fillRect(-width/2 - 3, height/2 - 20, 6, 15);
            ctx.fillRect(width/2 - 3, height/2 - 20, 6, 15);

            // Headlights for player car
            if (isPlayer) {
                ctx.fillStyle = '#FFFF99';
                ctx.fillRect(-width/2 + 8, -height/2 + 2, 8, 4);
                ctx.fillRect(width/2 - 16, -height/2 + 2, 8, 4);
            }

            ctx.restore();
        }

        // Draw power-up
        function drawPowerUp(powerUp) {
            ctx.save();
            ctx.translate(powerUp.x + powerUp.width/2, powerUp.y + powerUp.height/2);
            ctx.rotate(powerUp.rotation);

            // Glowing effect
            ctx.shadowColor = powerUp.color;
            ctx.shadowBlur = 15;

            // Power-up shape
            ctx.fillStyle = powerUp.color;
            ctx.beginPath();
            ctx.arc(0, 0, powerUp.width/2, 0, Math.PI * 2);
            ctx.fill();

            // Inner symbol
            ctx.fillStyle = '#fff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const symbol = powerUp.type === 'speed' ? '⚡' :
                          powerUp.type === 'score' ? '★' : '🛡️';
            ctx.fillText(symbol, 0, 0);

            ctx.restore();
        }
        
        // Enhanced 3D road with perspective
        function drawRoad() {
            const roadLeft = road.centerX - road.width / 2;
            const roadRight = road.centerX + road.width / 2;

            // Road surface with gradient for 3D effect
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#666');
            gradient.addColorStop(0.5, '#444');
            gradient.addColorStop(1, '#222');

            ctx.fillStyle = gradient;
            ctx.fillRect(roadLeft, 0, road.width, canvas.height);

            // Animated road markings for speed effect
            road.offset += car.speed * 2;
            if (road.offset > 40) road.offset = 0;

            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;

            // Center line with animation
            ctx.setLineDash([20, 20]);
            ctx.lineDashOffset = -road.offset;
            ctx.beginPath();
            ctx.moveTo(road.centerX, 0);
            ctx.lineTo(road.centerX, canvas.height);
            ctx.stroke();

            // Lane dividers with perspective
            const laneWidth = road.width / road.lanes;
            for (let i = 1; i < road.lanes; i++) {
                const x = roadLeft + i * laneWidth;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            ctx.setLineDash([]);

            // Road edges with perspective effect
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(roadLeft, 0);
            ctx.lineTo(roadLeft, canvas.height);
            ctx.moveTo(roadRight, 0);
            ctx.lineTo(roadRight, canvas.height);
            ctx.stroke();

            // Road shoulder/grass
            ctx.fillStyle = '#228B22';
            ctx.fillRect(0, 0, roadLeft, canvas.height);
            ctx.fillRect(roadRight, 0, canvas.width - roadRight, canvas.height);

            // Road reflections for wet effect
            if (Math.random() < 0.1) {
                ctx.save();
                ctx.globalAlpha = 0.3;
                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(roadLeft + Math.random() * road.width,
                           Math.random() * canvas.height,
                           20, 5);
                ctx.restore();
            }
        }
        
        // Environment objects
        const scenery = [];

        // Initialize scenery
        function initScenery() {
            for (let i = 0; i < 50; i++) {
                scenery.push({
                    x: (Math.random() - 0.5) * canvas.width * 3,
                    y: Math.random() * canvas.height * 2,
                    type: Math.random() > 0.5 ? 'tree' : 'building',
                    size: Math.random() * 30 + 20,
                    color: `hsl(${Math.random() * 60 + 100}, 50%, ${Math.random() * 30 + 30}%)`
                });
            }
        }

        // Draw environment with 3D perspective
        function drawEnvironment() {
            // Moving scenery for 3D effect
            for (const obj of scenery) {
                obj.y += car.speed * 0.3;
                if (obj.y > canvas.height + 100) {
                    obj.y = -100;
                    obj.x = (Math.random() - 0.5) * canvas.width * 3;
                }

                // Only draw if on sides of road
                if (Math.abs(obj.x - road.centerX) > road.width/2 + 30) {
                    const distance = Math.abs(obj.x - road.centerX) / canvas.width;
                    const scale = Math.max(0.3, 1 - distance);
                    const size = obj.size * scale;

                    ctx.save();
                    ctx.globalAlpha = scale;

                    if (obj.type === 'tree') {
                        // Tree
                        ctx.fillStyle = obj.color;
                        ctx.beginPath();
                        ctx.arc(obj.x, obj.y, size, 0, Math.PI * 2);
                        ctx.fill();

                        ctx.fillStyle = '#8B4513';
                        ctx.fillRect(obj.x - size/4, obj.y, size/2, size);
                    } else {
                        // Building
                        ctx.fillStyle = obj.color;
                        ctx.fillRect(obj.x - size/2, obj.y - size, size, size * 2);

                        // Windows
                        ctx.fillStyle = '#FFD700';
                        for (let w = 0; w < 3; w++) {
                            for (let h = 0; h < 4; h++) {
                                if (Math.random() > 0.3) {
                                    ctx.fillRect(
                                        obj.x - size/2 + w * size/3 + 2,
                                        obj.y - size + h * size/2 + 2,
                                        size/6, size/8
                                    );
                                }
                            }
                        }
                    }

                    ctx.restore();
                }
            }
        }
        
        // Update UI
        function updateUI() {
            document.getElementById('score').textContent = Math.floor(game.score);
            document.getElementById('distance').textContent = Math.floor(game.distance);
            document.getElementById('speed').textContent = Math.floor(game.speed);
        }
        
        // Main game loop
        function gameLoop() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Update game objects
            updateCar();
            updateTraffic();
            
            // Draw everything
            drawEnvironment();
            drawRoad();
            
            // Draw traffic cars
            for (const trafficCar of traffic) {
                drawCar(trafficCar.x, trafficCar.y, trafficCar.width, trafficCar.height, 0, trafficCar.color);
            }
            
            // Draw player car
            drawCar(car.x, car.y, car.width, car.height, car.angle);
            
            // Update UI
            updateUI();
            
            // Continue game loop
            requestAnimationFrame(gameLoop);
        }
        
        // Particle system for effects
        const particles = [];

        function createParticle(x, y, type = 'smoke') {
            const colors = {
                smoke: '#666',
                crash: '#ff4444',
                spark: '#FFD700'
            };

            particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * (type === 'crash' ? 8 : 4),
                vy: (Math.random() - 0.5) * (type === 'crash' ? 8 : 4),
                life: type === 'crash' ? 1.5 : 1.0,
                decay: type === 'crash' ? 0.03 : 0.02,
                size: Math.random() * (type === 'crash' ? 8 : 5) + 2,
                type: type,
                color: colors[type] || '#666'
            });
        }

        function updateParticles() {
            for (let i = particles.length - 1; i >= 0; i--) {
                const p = particles[i];
                p.x += p.vx;
                p.y += p.vy;
                p.life -= p.decay;

                if (p.life <= 0) {
                    particles.splice(i, 1);
                }
            }

            // Create exhaust particles when accelerating
            if (keys['ArrowUp'] || keys['KeyW']) {
                if (Math.random() < 0.3) {
                    createParticle(
                        car.x + car.width/2 + Math.random() * 10 - 5,
                        car.y + car.height + 5,
                        'smoke'
                    );
                }
            }
        }

        function drawParticles() {
            for (const p of particles) {
                ctx.save();
                ctx.globalAlpha = p.life;
                ctx.fillStyle = p.color;
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.size * p.life, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // Draw UI messages
        function drawMessages() {
            if (messageTimer > 0) {
                messageTimer--;

                ctx.save();
                ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                ctx.fillRect(canvas.width/2 - 150, canvas.height/2 - 30, 300, 60);

                ctx.fillStyle = '#fff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(currentMessage, canvas.width/2, canvas.height/2);
                ctx.restore();
            }

            // Power-up status indicators
            let statusY = 100;
            if (speedBoost) {
                ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
                ctx.fillRect(canvas.width - 200, statusY, 180, 30);
                ctx.fillStyle = '#000';
                ctx.font = '16px Arial';
                ctx.fillText('⚡ SPEED BOOST ACTIVE', canvas.width - 110, statusY + 20);
                statusY += 40;
            }

            if (scoreMultiplier > 1) {
                ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
                ctx.fillRect(canvas.width - 200, statusY, 180, 30);
                ctx.fillStyle = '#000';
                ctx.font = '16px Arial';
                ctx.fillText('★ SCORE x2 ACTIVE', canvas.width - 110, statusY + 20);
                statusY += 40;
            }

            if (shieldActive) {
                ctx.fillStyle = 'rgba(0, 191, 255, 0.8)';
                ctx.fillRect(canvas.width - 200, statusY, 180, 30);
                ctx.fillStyle = '#000';
                ctx.font = '16px Arial';
                ctx.fillText('🛡️ SHIELD ACTIVE', canvas.width - 110, statusY + 20);
            }
        }

        // Enhanced main game loop
        function gameLoop() {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Update game objects
            updateCar();
            updateTraffic();
            updateParticles();

            // Draw everything in layers
            drawEnvironment();
            drawRoad();
            drawParticles();

            // Draw power-ups
            for (const powerUp of powerUps) {
                drawPowerUp(powerUp);
            }

            // Draw traffic cars
            for (const trafficCar of traffic) {
                drawCar(trafficCar.x, trafficCar.y, trafficCar.width, trafficCar.height, 0, trafficCar.color, false);
            }

            // Draw player car with effects
            drawCar(car.x, car.y, car.width, car.height, car.angle, '#ff4444', true);

            // Draw UI elements
            drawMessages();
            updateUI();

            // Continue game loop
            requestAnimationFrame(gameLoop);
        }

        // Initialize and start the game
        initScenery();

        console.log('🏁 3D Car Racing Game Started!');
        console.log('🎮 Use arrow keys or WASD to control your car');
        console.log('🚗 Avoid traffic and score points!');
        console.log('💨 Watch for exhaust particles when accelerating!');

        gameLoop();
    </script>
</body>
</html>
